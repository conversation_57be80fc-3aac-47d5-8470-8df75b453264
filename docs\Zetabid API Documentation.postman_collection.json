{"info": {"_postman_id": "c3ad24a1-0ddf-4518-80db-5c56b01c7fc6", "name": "Zetabid API Documentation", "description": "This is the API documentation for Admin, Customer, and Vendor routes, including login, password reset, and authenticated routes.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "17671100", "_collection_link": "https://zettabid.postman.co/workspace/ZettaBid-Workspace~0f95f98a-f588-408f-813a-320305cca71b/collection/17671100-c3ad24a1-0ddf-4518-80db-5c56b01c7fc6?action=share&source=collection_link&creator=17671100"}, "item": [{"name": "Admin", "item": [{"name": "Business Category", "item": [{"name": "List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/business-category", "host": ["{{base_url}}"], "path": ["api", "admin", "business-category"]}}, "response": []}, {"name": "Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "oioioiohaiiii", "type": "text"}, {"key": "description", "value": "qweqwe", "type": "text"}, {"key": "image", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/4409951.jpg"}, {"key": "parent_id", "value": "3", "type": "text", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/admin/business-category/1?_method=PUT", "host": ["{{base_url}}"], "path": ["api", "admin", "business-category", "1"], "query": [{"key": "_method", "value": "PUT"}]}}, "response": []}, {"name": "Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/business-category/2", "host": ["{{base_url}}"], "path": ["api", "admin", "business-category", "2"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "<PERSON> sita", "type": "text"}, {"key": "description", "value": "qweqwe", "type": "text"}, {"key": "image", "value": "", "type": "text"}, {"key": "parent_id", "value": "2", "type": "text", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/admin/business-category", "host": ["{{base_url}}"], "path": ["api", "admin", "business-category"]}}, "response": []}, {"name": "Show", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/business-category/1", "host": ["{{base_url}}"], "path": ["api", "admin", "business-category", "1"]}}, "response": []}]}, {"name": "Training Session", "item": [{"name": "List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/training-session", "host": ["{{base_url}}"], "path": ["api", "admin", "training-session"]}}, "response": []}, {"name": "Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Hare Ram sita updated", "type": "text"}, {"key": "description", "value": "qweqwe", "type": "text"}, {"key": "meeting_link", "value": "https://zettabid-test.vercel.app/admin/1/support-and-training/training", "type": "text"}, {"key": "start_datetime", "value": "2025-05-05 18:05:56", "type": "text"}, {"key": "end_datetime", "value": "2025-05-05 19:05:56", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/admin/training-session/2?_method=PUT", "host": ["{{base_url}}"], "path": ["api", "admin", "training-session", "2"], "query": [{"key": "_method", "value": "PUT"}]}}, "response": []}, {"name": "Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/training-session/2", "host": ["{{base_url}}"], "path": ["api", "admin", "training-session", "2"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Training one", "type": "text"}, {"key": "description", "value": "qweqwe", "type": "text"}, {"key": "meeting_link", "value": "https://zettabid-test.vercel.app/admin/1/support-and-training/training", "type": "text"}, {"key": "start_datetime", "value": "2025-05-05 18:05:56", "type": "text"}, {"key": "end_datetime", "value": "2025-05-05 19:05:56", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/admin/training-session", "host": ["{{base_url}}"], "path": ["api", "admin", "training-session"]}}, "response": []}, {"name": "Show", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/training-session/1", "host": ["{{base_url}}"], "path": ["api", "admin", "training-session", "1"]}}, "response": []}]}, {"name": "FAQs", "item": [{"name": "List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/faq", "host": ["{{base_url}}"], "path": ["api", "admin", "faq"]}}, "response": []}, {"name": "Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "question", "value": "Hare Ram sita updated", "type": "text"}, {"key": "answer", "value": "qweqwe zzz", "type": "text"}, {"key": "category", "value": "General", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/admin/faq/2?_method=PUT", "host": ["{{base_url}}"], "path": ["api", "admin", "faq", "2"], "query": [{"key": "_method", "value": "PUT"}]}}, "response": []}, {"name": "Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/faq/2", "host": ["{{base_url}}"], "path": ["api", "admin", "faq", "2"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "question", "value": "<PERSON> sita", "type": "text"}, {"key": "answer", "value": "qweqwe zzz", "type": "text"}, {"key": "category", "value": "General", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/admin/faq", "host": ["{{base_url}}"], "path": ["api", "admin", "faq"]}}, "response": []}, {"name": "Show", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/faq/1", "host": ["{{base_url}}"], "path": ["api", "admin", "faq", "1"]}}, "response": []}]}, {"name": "Notice", "item": [{"name": "List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/notice", "host": ["{{base_url}}"], "path": ["api", "admin", "notice"]}}, "response": []}, {"name": "Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Hare Ram sita updated", "type": "text"}, {"key": "description", "value": "qweqwe zzz", "type": "text"}, {"key": "attachment", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/achar-offer-2.jpg"}]}, "url": {"raw": "{{base_url}}/api/admin/notice/2?_method=PUT", "host": ["{{base_url}}"], "path": ["api", "admin", "notice", "2"], "query": [{"key": "_method", "value": "PUT"}]}}, "response": []}, {"name": "Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/admin/notice/2", "host": ["{{base_url}}"], "path": ["api", "admin", "notice", "2"]}}, "response": []}, {"name": "Store", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Hello Notice", "type": "text"}, {"key": "description", "value": "qweqwe zzz", "type": "text"}, {"key": "attachment", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/achar-offer-1.jpg"}]}, "url": {"raw": "{{base_url}}/api/admin/notice", "host": ["{{base_url}}"], "path": ["api", "admin", "notice"]}}, "response": []}, {"name": "Show", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/notice/1", "host": ["{{base_url}}"], "path": ["api", "admin", "notice", "1"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "admin@123", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/admin/login", "host": ["{{base_url}}"], "path": ["api", "admin", "login"]}}, "response": []}, {"name": "Password Reset Request", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/password-reset-request", "host": ["{{base_url}}"], "path": ["api", "admin", "password-reset-request"]}}, "response": []}, {"name": "Reset Password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\", \"token\": \"9535\", \"password\": \"newpassword\", \"password_confirmation\": \"newpassword\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/reset-password", "host": ["{{base_url}}"], "path": ["api", "admin", "reset-password"]}}, "response": []}, {"name": "Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "1|OVTYdVs0SdDSGa4ph5i0HVJa5Egug10yTBYA4feIcf3d7511", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/admin/logout", "host": ["{{base_url}}"], "path": ["api", "admin", "logout"]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "1|OVTYdVs0SdDSGa4ph5i0HVJa5Egug10yTBYA4feIcf3d7511", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/admin/refresh", "host": ["{{base_url}}"], "path": ["api", "admin", "refresh"]}}, "response": []}]}, {"name": "Page", "item": [{"name": "About Us", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "qweqwe updated test", "type": "text"}, {"key": "description", "value": "qqqqqqqqqq", "type": "text"}, {"key": "introduction", "value": "", "type": "text"}, {"key": "team", "value": "", "type": "text"}, {"key": "values", "value": "", "type": "text"}, {"key": "meta_title", "value": "", "type": "text"}, {"key": "meta_description", "value": "", "type": "text"}, {"key": "image", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/5694096.jpg"}]}, "url": {"raw": "{{base_url}}/api/admin/about-us?_method=PUT", "host": ["{{base_url}}"], "path": ["api", "admin", "about-us"], "query": [{"key": "_method", "value": "PUT"}]}}, "response": []}, {"name": "Contact Us", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Contact us updated", "type": "text"}, {"key": "description", "value": "This is description of contact us", "type": "text"}, {"key": "meta_title", "value": "", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/admin/contact-us?_method=PUT", "host": ["{{base_url}}"], "path": ["api", "admin", "contact-us"], "query": [{"key": "_method", "value": "PUT"}]}}, "response": []}, {"name": "About Us", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/admin/about-us", "host": ["{{base_url}}"], "path": ["api", "admin", "about-us"]}}, "response": []}, {"name": "Contact Us", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/admin/contact-us", "host": ["{{base_url}}"], "path": ["api", "admin", "contact-us"]}}, "response": []}]}, {"name": "Get Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/admin/profile", "host": ["{{base_url}}"], "path": ["api", "admin", "profile"]}}, "response": []}, {"name": "Update Profile", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "_method", "value": "PUT", "type": "text"}, {"key": "name", "value": "Main Admin", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "profile", "type": "file", "src": "postman-cloud:///1ef68fbd-d1cb-41e0-8d6d-ccefc77af1de"}]}, "url": {"raw": "{{base_url}}/api/admin/profile", "host": ["{{base_url}}"], "path": ["api", "admin", "profile"]}}, "response": []}, {"name": "Dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/dashboard", "host": ["{{base_url}}"], "path": ["api", "admin", "dashboard"]}}, "response": []}, {"name": "Change Password", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"admin@123\",\n    \"new_password\": \"newpassword\",\n    \"new_password_confirmation\": \"newpassword\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/change-password", "host": ["{{base_url}}"], "path": ["api", "admin", "change-password"]}}, "response": []}, {"name": "Roles Management", "item": [{"name": "List Roles", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/roles", "host": ["{{base_url}}"], "path": ["api", "admin", "roles"]}}, "response": []}, {"name": "Create Role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"test_role\",\n    \"display_name\": \"Test Role\",\n    \"description\": \"Test role description\",\n    \"permissions\": [1, 2, 3]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/roles", "host": ["{{base_url}}"], "path": ["api", "admin", "roles"]}}, "response": []}, {"name": "Show Role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/roles/1", "host": ["{{base_url}}"], "path": ["api", "admin", "roles", "1"]}}, "response": []}, {"name": "Update Role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"updated_role\",\n    \"display_name\": \"Updated Role\",\n    \"description\": \"Updated role description\",\n    \"permissions\": [1, 2, 3, 4]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/roles/1", "host": ["{{base_url}}"], "path": ["api", "admin", "roles", "1"]}}, "response": []}, {"name": "Delete Role", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/roles/1", "host": ["{{base_url}}"], "path": ["api", "admin", "roles", "1"]}}, "response": []}]}, {"name": "Permissions Management", "item": [{"name": "List Permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/permissions", "host": ["{{base_url}}"], "path": ["api", "admin", "permissions"]}}, "response": []}, {"name": "Create Permission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"test_permission\",\n    \"display_name\": \"Test Permission\",\n    \"description\": \"Test permission description\",\n    \"group\": \"test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/permissions", "host": ["{{base_url}}"], "path": ["api", "admin", "permissions"]}}, "response": []}, {"name": "Show Permission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/permissions/1", "host": ["{{base_url}}"], "path": ["api", "admin", "permissions", "1"]}}, "response": []}, {"name": "Update Permission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"updated_permission\",\n    \"display_name\": \"Updated Permission\",\n    \"description\": \"Updated permission description\",\n    \"group\": \"updated_group\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/permissions/1", "host": ["{{base_url}}"], "path": ["api", "admin", "permissions", "1"]}}, "response": []}, {"name": "Delete Permission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/permissions/1", "host": ["{{base_url}}"], "path": ["api", "admin", "permissions", "1"]}}, "response": []}, {"name": "Get Grouped Permissions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/permissions/grouped", "host": ["{{base_url}}"], "path": ["api", "admin", "permissions", "grouped"]}}, "response": []}]}, {"name": "User Role Management", "item": [{"name": "Get Users by Type", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/users/admin", "host": ["{{base_url}}"], "path": ["api", "admin", "users", "admin"]}}, "response": []}, {"name": "Get User Roles", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/admin/users/admin/1/roles", "host": ["{{base_url}}"], "path": ["api", "admin", "users", "admin", "1", "roles"]}}, "response": []}, {"name": "Assign Roles to User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"roles\": [1, 2, 3]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/users/admin/1/roles", "host": ["{{base_url}}"], "path": ["api", "admin", "users", "admin", "1", "roles"]}}, "response": []}, {"name": "Remove Roles from User", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{admin_access_token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"roles\": [1, 2]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/admin/users/admin/1/roles", "host": ["{{base_url}}"], "path": ["api", "admin", "users", "admin", "1", "roles"]}}, "response": []}]}]}, {"name": "Customer", "item": [{"name": "Business Category", "item": [{"name": "Sub-categories By Category", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{customer_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/customer/business-category/2/subcategories", "host": ["{{base_url}}"], "path": ["api", "customer", "business-category", "2", "subcategories"]}}, "response": []}, {"name": "List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{customer_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/customer/business-category", "host": ["{{base_url}}"], "path": ["api", "customer", "business-category"]}}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"customer@123\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/customer/login", "host": ["{{base_url}}"], "path": ["api", "customer", "login"]}}, "response": []}, {"name": "Regsiter", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"Hello\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"customerpassword\",\r\n    \"password_confirmation\": \"customerpassword\",\r\n    \"type\": \"individual\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/customer/register", "host": ["{{base_url}}"], "path": ["api", "customer", "register"]}}, "response": []}, {"name": "Password Reset Request", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/customer/password-reset-request", "host": ["{{base_url}}"], "path": ["api", "customer", "password-reset-request"]}}, "response": []}, {"name": "Reset Password", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"<EMAIL>\",\r\n    \"token\": \"3275\",\r\n    \"password\": \"newpassword\",\r\n    \"password_confirmation\": \"newpassword\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/customer/reset-password", "host": ["{{base_url}}"], "path": ["api", "customer", "reset-password"]}}, "response": []}, {"name": "Logout", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "7|CDHugY96ZZdyvJb7uu6H40UZQsqFK1yLo19VlwHzcc3b7eeb", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{customer_token}}"}], "url": {"raw": "{{base_url}}/api/customer/logout", "host": ["{{base_url}}"], "path": ["api", "customer", "logout"]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "8|7zUne8XLLGmEZSbzlaewqGQAJq74qL427bb9Me4A614b426e", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{customer_token}}"}], "url": {"raw": "{{base_url}}/api/customer/refresh", "host": ["{{base_url}}"], "path": ["api", "customer", "refresh"]}}, "response": []}, {"name": "Change Password", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "8|7zUne8XLLGmEZSbzlaewqGQAJq74qL427bb9Me4A614b426e", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"current_password\": \"oldpassword\", \"new_password\": \"newpassword\", \"new_password_confirmation\": \"newpassword\"}", "options": {"raw": {"language": "text"}}}, "url": {"raw": "{{base_url}}/api/customer/change-password", "host": ["{{base_url}}"], "path": ["api", "customer", "change-password"]}}, "response": []}]}, {"name": "Get Profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{customer_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{customer_token}}"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{base_url}}/api/customer/profile", "host": ["{{base_url}}"], "path": ["api", "customer", "profile"]}}, "response": []}, {"name": "Profile", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{customer_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "_method", "value": "PUT", "type": "text"}, {"key": "name", "value": "Bikal Customer", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "profile", "type": "file", "src": "postman-cloud:///1ef68fbd-d1cb-41e0-8d6d-ccefc77af1de"}, {"key": "address", "value": "<PERSON><PERSON><PERSON>", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/customer/profile", "host": ["{{base_url}}"], "path": ["api", "customer", "profile"]}}, "response": []}, {"name": "KYC Verify", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{customer_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "_method", "value": "PUT", "type": "text"}, {"key": "name", "value": "Bikal Customer", "type": "text"}, {"key": "citizenship", "type": "file", "src": ["postman-cloud:///1ef68fbd-d1cb-41e0-8d6d-ccefc77af1de", "/C:/Users/<USER>/Desktop/img/5694096.jpg"]}, {"key": "pan", "type": "file", "src": []}, {"key": "phone", "value": "9849955681", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/customer/verify-kyc", "host": ["{{base_url}}"], "path": ["api", "customer", "verify-kyc"]}}, "response": []}, {"name": "Dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{customer_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/customer/dashboard", "host": ["{{base_url}}"], "path": ["api", "customer", "dashboard"]}}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "<PERSON><PERSON>", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "vendor@123", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/vendor/login", "host": ["{{base_url}}"], "path": ["api", "vendor", "login"]}}, "response": []}, {"name": "Password Reset Request", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\"}"}, "url": {"raw": "{{base_url}}/api/vendor/password-reset-request", "host": ["{{base_url}}"], "path": ["api", "vendor", "password-reset-request"]}}, "response": []}, {"name": "Reset Password", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\"email\": \"<EMAIL>\", \"token\": \"reset-token\", \"password\": \"newpassword\", \"password_confirmation\": \"newpassword\"}"}, "url": {"raw": "{{base_url}}/api/vendor/reset-password", "host": ["{{base_url}}"], "path": ["api", "vendor", "reset-password"]}}, "response": []}, {"name": "Register", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Bikal Vendor 1", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "password", "value": "vendor@123", "type": "text"}, {"key": "password_confirmation", "value": "vendor@123", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/vendor/register", "host": ["{{base_url}}"], "path": ["api", "vendor", "register"]}}, "response": []}, {"name": "Refresh <PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "8|7zUne8XLLGmEZSbzlaewqGQAJq74qL427bb9Me4A614b426e", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{customer_token}}"}], "url": {"raw": "{{base_url}}/api/vendor/refresh", "host": ["{{base_url}}"], "path": ["api", "vendor", "refresh"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{vendor_token}}"}], "url": {"raw": "{{base_url}}/api/vendor/logout", "host": ["{{base_url}}"], "path": ["api", "vendor", "logout"]}}, "response": []}, {"name": "Change Password", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "8|7zUne8XLLGmEZSbzlaewqGQAJq74qL427bb9Me4A614b426e", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\"current_password\": \"oldpassword\", \"new_password\": \"newpassword\", \"new_password_confirmation\": \"newpassword\"}", "options": {"raw": {"language": "text"}}}, "url": {"raw": "{{base_url}}/api/vendor/change-password", "host": ["{{base_url}}"], "path": ["api", "vendor", "change-password"]}}, "response": []}]}, {"name": "Profile - KYC", "item": [{"name": "Company Profile", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Mero Company", "type": "text"}, {"key": "logo", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/achar-offer-1.jpg"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "company_representative", "value": "Bikal", "type": "text"}, {"key": "phone", "value": "***********", "type": "text"}, {"key": "province", "value": "1", "type": "text"}, {"key": "district", "value": "6", "type": "text"}, {"key": "municipality", "value": "271", "type": "text"}, {"key": "address", "value": "Haraincha 02", "type": "text"}, {"key": "business_type", "value": "hello type", "type": "text"}, {"key": "website", "value": "", "type": "text"}, {"key": "company_registration", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/achar-offer-2.jpg"}, {"key": "vat_pan", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/4409951.jpg"}, {"key": "tax_clearance", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/5.jpg"}, {"key": "additional_information", "type": "file", "src": []}, {"key": "ward_no", "value": "1", "type": "text"}, {"key": "street_address", "value": "Hello", "type": "text"}, {"key": "tole", "value": "", "type": "text"}, {"key": "bank_name", "value": "bbb", "type": "text"}, {"key": "bank_branch", "value": "", "type": "text"}, {"key": "bank_account_number", "value": "", "type": "text"}, {"key": "account_holder_name", "value": "", "type": "text"}, {"key": "audit_report", "type": "file", "src": []}, {"key": "signature", "type": "file", "src": "/C:/Users/<USER>/Desktop/img/watch.jpg"}]}, "url": {"raw": "{{base_url}}/api/vendor/company-profile", "host": ["{{base_url}}"], "path": ["api", "vendor", "company-profile"]}}, "response": []}, {"name": "Company Profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/vendor/company-profile/show", "host": ["{{base_url}}"], "path": ["api", "vendor", "company-profile", "show"]}}, "response": []}]}, {"name": "Business Category", "item": [{"name": "List", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/vendor/business-category", "host": ["{{base_url}}"], "path": ["api", "vendor", "business-category"]}}, "response": []}, {"name": "Sub-categories By Category", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "", "value": "", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/vendor/business-category/2/subcategories", "host": ["{{base_url}}"], "path": ["api", "vendor", "business-category", "2", "subcategories"]}}, "response": []}]}, {"name": "Province Get", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/provinces", "host": ["{{base_url}}"], "path": ["api", "provinces"]}}, "response": []}, {"name": "Municipalities By District", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/municipalities-by-district/6", "host": ["{{base_url}}"], "path": ["api", "municipalities-by-district", "6"]}}, "response": []}, {"name": "Districts By Province", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/districts-by-province/1", "host": ["{{base_url}}"], "path": ["api", "districts-by-province", "1"]}}, "response": []}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{vendor_token}}"}], "url": {"raw": "{{base_url}}/api/vendor/profile", "host": ["{{base_url}}"], "path": ["api", "vendor", "profile"]}}, "response": []}, {"name": "Update Profile", "protocolProfileBehavior": {"disabledSystemHeaders": {"accept": true}}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "10|J1BjgurrfPIwp02gvfeYL7Bn3EaA5kitBoGeZLRHf07792a5", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "_method", "value": "PUT", "type": "text"}, {"key": "name", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "profile", "type": "file", "src": "postman-cloud:///1ef68fbd-d1cb-41e0-8d6d-ccefc77af1de"}, {"key": "address", "value": "khanar", "type": "text"}]}, "url": {"raw": "{{base_url}}/api/vendor/profile", "host": ["{{base_url}}"], "path": ["api", "vendor", "profile"]}}, "response": []}, {"name": "Dashboard", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/vendor/dashboard", "host": ["{{base_url}}"], "path": ["api", "vendor", "dashboard"]}}, "response": []}, {"name": "Company Profile Show", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/vendor/company-profile", "host": ["{{base_url}}"], "path": ["api", "vendor", "company-profile"]}}, "response": []}, {"name": "Business Category List", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/vendor/business-category", "host": ["{{base_url}}"], "path": ["api", "vendor", "business-category"]}}, "response": []}, {"name": "Business Category Subcategories", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{vendor_access_token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}], "url": {"raw": "{{base_url}}/api/vendor/business-category/1/subcategories", "host": ["{{base_url}}"], "path": ["api", "vendor", "business-category", "1", "subcategories"]}}, "response": []}]}, {"name": "Client", "item": [{"name": "About Us", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/about-us", "host": ["{{base_url}}"], "path": ["api", "about-us"]}}, "response": []}, {"name": "Contact Us", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/contact-us", "host": ["{{base_url}}"], "path": ["api", "contact-us"]}}, "response": []}, {"name": "Training Session", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/training-session", "host": ["{{base_url}}"], "path": ["api", "training-session"]}}, "response": []}, {"name": "FAQ", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/faq", "host": ["{{base_url}}"], "path": ["api", "faq"]}}, "response": []}, {"name": "Notice", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{base_url}}/api/notice", "host": ["{{base_url}}"], "path": ["api", "notice"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://test1.maurisys.com", "type": "string"}]}